# Netflix Skipper Chrome Extension

A Chrome extension that provides custom arrow key controls for Netflix video playback with configurable skip intervals.

## Features

- **Custom Arrow Key Controls**: Use left (←) and right (→) arrow keys to skip backward and forward in Netflix videos
- **Configurable Skip Interval**: Set your preferred skip duration (1-60 seconds, default: 5 seconds)
- **Visual Feedback**: Shows a brief indicator when skipping
- **Local Storage**: Settings are saved and synced across browser sessions
- **Netflix-Only**: Only activates on Netflix.com, doesn't interfere with other websites

## Installation

### Development Installation

1. Clone this repository:
   ```bash
   git clone <repository-url>
   cd netflix-skipper
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Build the extension:
   ```bash
   npm run build
   ```

4. Load the extension in Chrome:
   - Open Chrome and go to `chrome://extensions/`
   - Enable "Developer mode" in the top right
   - Click "Load unpacked" and select the `dist` folder

## Usage

1. **Install the extension** following the instructions above
2. **Go to Netflix** (https://www.netflix.com) and start watching any video
3. **Use arrow keys** to skip:
   - **Left Arrow (←)**: Skip backward by your configured interval
   - **Right Arrow (→)**: Skip forward by your configured interval
4. **Configure settings** by clicking the extension icon in the Chrome toolbar

## Configuration

Click the Netflix Skipper icon in your Chrome toolbar to open the settings popup where you can:

- **Adjust Skip Interval**: Set how many seconds to skip (1-60 seconds)
- **View Current Settings**: See your current configuration

Settings are automatically saved and will persist across browser sessions.

## Development

### Project Structure

```
src/
├── content/
│   ├── main.tsx              # Content script entry point
│   └── netflixController.ts  # Main Netflix video controller
├── popup/
│   ├── App.tsx              # Settings popup interface
│   ├── App.css              # Popup styles
│   └── index.html           # Popup HTML
└── utils/
    └── storage.ts           # Chrome storage utilities
```

### Building

```bash
npm run build    # Build for production
npm run dev      # Development mode with hot reload
```

### Technologies Used

- **React 19** with TypeScript
- **Vite** for building and development
- **CRXJS** for Chrome extension development
- **Chrome Extensions Manifest V3**

## How It Works

1. **Content Script**: Injects into Netflix pages and listens for arrow key events
2. **Event Interception**: Prevents default Netflix arrow key behavior
3. **Video Control**: Directly manipulates the HTML5 video element's `currentTime` property
4. **Settings Management**: Uses Chrome's storage API to persist user preferences
5. **Visual Feedback**: Shows temporary overlay indicators for skip actions

## Permissions

The extension requires minimal permissions:
- **storage**: To save and retrieve user settings
- **Netflix.com access**: To inject the content script only on Netflix pages

## Troubleshooting

- **Arrow keys not working**: Make sure you're on a Netflix video page and the video is loaded
- **Settings not saving**: Check that the extension has storage permissions
- **Extension not loading**: Ensure you've built the project and loaded the `dist` folder
