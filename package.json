{"name": "netflix-skipper", "type": "module", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@crxjs/vite-plugin": "^2.0.0", "@types/chrome": "^0.0.313", "@types/node": "^22.13.14", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "typescript": "~5.7.2", "vite": "^6.2.0", "vite-plugin-zip-pack": "^1.2.4"}}