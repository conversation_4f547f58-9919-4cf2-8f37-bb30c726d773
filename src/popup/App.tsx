import { useState, useEffect } from 'react';
import { StorageManager, NetflixSkipperSettings, DEFAULT_SETTINGS } from '../utils/storage';
import './App.css';

export default function App() {
  const [settings, setSettings] = useState<NetflixSkipperSettings>(DEFAULT_SETTINGS);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // Load settings on component mount
  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const loadedSettings = await StorageManager.getSettings();
      setSettings(loadedSettings);
    } catch (error) {
      console.error('Failed to load settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveSettings = async (newSettings: NetflixSkipperSettings) => {
    setIsSaving(true);
    try {
      await StorageManager.saveSettings(newSettings);
      setSettings(newSettings);
    } catch (error) {
      console.error('Failed to save settings:', error);
      alert('Failed to save settings. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleSkipIntervalChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(event.target.value, 10);
    if (value > 0 && value <= 60) {
      const newSettings = { ...settings, skipInterval: value };
      saveSettings(newSettings);
    }
  };

  if (isLoading) {
    return (
      <div className="popup-container">
        <div className="loading">Loading...</div>
      </div>
    );
  }

  return (
    <div className="popup-container">
      <div className="header">
        <h1>Netflix Skipper</h1>
        <p>Custom arrow key controls for Netflix</p>
      </div>

      <div className="settings">
        <div className="setting-item">
          <label htmlFor="skip-interval">Skip Interval (seconds):</label>
          <input
            id="skip-interval"
            type="number"
            min="1"
            max="60"
            value={settings.skipInterval}
            onChange={handleSkipIntervalChange}
            disabled={isSaving}
          />
        </div>

        <div className="info">
          <p>Use ← and → arrow keys on Netflix to skip by {settings.skipInterval} seconds</p>
        </div>

        {isSaving && <div className="saving">Saving...</div>}
      </div>
    </div>
  );
}
