.popup-container {
  width: 300px;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #ffffff;
  color: #333;
}

.header {
  text-align: center;
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
}

.header h1 {
  margin: 0 0 5px 0;
  font-size: 18px;
  font-weight: 600;
  color: #e50914;
}

.header p {
  margin: 0;
  font-size: 12px;
  color: #666;
}

.settings {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.setting-item label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.setting-item input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.setting-item input:focus {
  outline: none;
  border-color: #e50914;
  box-shadow: 0 0 0 2px rgba(229, 9, 20, 0.1);
}

.setting-item input:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

.info {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  border-left: 3px solid #e50914;
}

.info p {
  margin: 0;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.loading, .saving {
  text-align: center;
  font-size: 12px;
  color: #666;
  font-style: italic;
}

.saving {
  margin-top: 10px;
}
