import { StorageManager, NetflixSkipperSettings, DEFAULT_SETTINGS } from '../utils/storage';

export class NetflixVideoController {
  private settings: NetflixSkipperSettings = DEFAULT_SETTINGS;
  private isInitialized = false;

  constructor() {
    this.handleKeyDown = this.handleKeyDown.bind(this);
    this.loadSettings();
  }

  /**
   * Initialize the controller
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    console.log('[Netflix Skipper] Initializing video controller');
    
    // Load settings from storage
    await this.loadSettings();
    
    // Listen for settings changes
    StorageManager.onSettingsChanged((newSettings) => {
      this.settings = newSettings;
      console.log('[Netflix Skipper] Settings updated:', newSettings);
    });

    // Add keyboard event listener
    document.addEventListener('keydown', this.handleKeyDown, true);
    
    this.isInitialized = true;
    console.log('[Netflix Skipper] Controller initialized with skip interval:', this.settings.skipInterval);
  }

  /**
   * Load settings from storage
   */
  private async loadSettings(): Promise<void> {
    try {
      this.settings = await StorageManager.getSettings();
    } catch (error) {
      console.error('[Netflix Skipper] Failed to load settings:', error);
      this.settings = DEFAULT_SETTINGS;
    }
  }

  /**
   * Handle keydown events
   */
  private handleKeyDown(event: KeyboardEvent): void {
    // Only handle arrow keys
    if (event.key !== 'ArrowLeft' && event.key !== 'ArrowRight') {
      return;
    }

    // Only handle if we're on Netflix and there's a video playing
    if (!this.isOnNetflix() || !this.getNetflixVideo()) {
      return;
    }

    // Prevent default behavior
    event.preventDefault();
    event.stopPropagation();
    event.stopImmediatePropagation();

    // Handle the skip
    if (event.key === 'ArrowLeft') {
      this.skipBackward();
    } else if (event.key === 'ArrowRight') {
      this.skipForward();
    }
  }

  /**
   * Check if we're on Netflix
   */
  private isOnNetflix(): boolean {
    return window.location.hostname === 'www.netflix.com';
  }

  /**
   * Get the Netflix video element
   */
  private getNetflixVideo(): HTMLVideoElement | null {
    // Netflix uses video elements, try to find the main video
    const videos = document.querySelectorAll('video');
    
    // Find the video that's currently playing or has the largest dimensions
    let mainVideo: HTMLVideoElement | null = null;
    let maxArea = 0;

    for (const video of videos) {
      const area = video.videoWidth * video.videoHeight;
      if (area > maxArea && video.duration > 0) {
        maxArea = area;
        mainVideo = video;
      }
    }

    return mainVideo;
  }

  /**
   * Skip forward by the configured interval
   */
  private skipForward(): void {
    const video = this.getNetflixVideo();
    if (!video) return;

    const newTime = Math.min(video.currentTime + this.settings.skipInterval, video.duration);
    video.currentTime = newTime;
    
    console.log(`[Netflix Skipper] Skipped forward ${this.settings.skipInterval}s to ${newTime.toFixed(1)}s`);
    this.showSkipIndicator('forward', this.settings.skipInterval);
  }

  /**
   * Skip backward by the configured interval
   */
  private skipBackward(): void {
    const video = this.getNetflixVideo();
    if (!video) return;

    const newTime = Math.max(video.currentTime - this.settings.skipInterval, 0);
    video.currentTime = newTime;
    
    console.log(`[Netflix Skipper] Skipped backward ${this.settings.skipInterval}s to ${newTime.toFixed(1)}s`);
    this.showSkipIndicator('backward', this.settings.skipInterval);
  }

  /**
   * Show a visual indicator for the skip action
   */
  private showSkipIndicator(direction: 'forward' | 'backward', seconds: number): void {
    // Remove any existing indicator
    const existingIndicator = document.getElementById('netflix-skipper-indicator');
    if (existingIndicator) {
      existingIndicator.remove();
    }

    // Create new indicator
    const indicator = document.createElement('div');
    indicator.id = 'netflix-skipper-indicator';
    indicator.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      font-family: Arial, sans-serif;
      font-size: 16px;
      font-weight: bold;
      z-index: 10000;
      pointer-events: none;
      transition: opacity 0.3s ease;
    `;
    
    const arrow = direction === 'forward' ? '→' : '←';
    indicator.textContent = `${arrow} ${seconds}s`;
    
    document.body.appendChild(indicator);
    
    // Remove after 1 second
    setTimeout(() => {
      indicator.style.opacity = '0';
      setTimeout(() => {
        if (indicator.parentNode) {
          indicator.parentNode.removeChild(indicator);
        }
      }, 300);
    }, 700);
  }

  /**
   * Cleanup when the controller is destroyed
   */
  destroy(): void {
    if (this.isInitialized) {
      document.removeEventListener('keydown', this.handleKeyDown, true);
      this.isInitialized = false;
      console.log('[Netflix Skipper] Controller destroyed');
    }
  }
}
