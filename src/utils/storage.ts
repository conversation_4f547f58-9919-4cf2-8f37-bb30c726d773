export interface NetflixSkipperSettings {
  skipInterval: number; // in seconds
}

export const DEFAULT_SETTINGS: NetflixSkipperSettings = {
  skipInterval: 5
};

export class StorageManager {
  private static readonly STORAGE_KEY = 'netflix-skipper-settings';

  /**
   * Get settings from Chrome storage
   */
  static async getSettings(): Promise<NetflixSkipperSettings> {
    try {
      const result = await chrome.storage.local.get(this.STORAGE_KEY);
      return result[this.STORAGE_KEY] || DEFAULT_SETTINGS;
    } catch (error) {
      console.error('Failed to get settings from storage:', error);
      return DEFAULT_SETTINGS;
    }
  }

  /**
   * Save settings to Chrome storage
   */
  static async saveSettings(settings: NetflixSkipperSettings): Promise<void> {
    try {
      await chrome.storage.local.set({
        [this.STORAGE_KEY]: settings
      });
    } catch (error) {
      console.error('Failed to save settings to storage:', error);
      throw error;
    }
  }

  /**
   * Listen for storage changes
   */
  static onSettingsChanged(callback: (settings: NetflixSkipperSettings) => void): void {
    chrome.storage.onChanged.addListener((changes, areaName) => {
      if (areaName === 'local' && changes[this.STORAGE_KEY]) {
        callback(changes[this.STORAGE_KEY].newValue || DEFAULT_SETTINGS);
      }
    });
  }
}
