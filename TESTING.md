# Netflix Skipper - Testing Instructions

## Installation for Testing

1. **Build the extension** (already done):
   ```bash
   npm run build
   ```

2. **Load in Chrome**:
   - Open Chrome and go to `chrome://extensions/`
   - Enable "Developer mode" (toggle in top right)
   - Click "Load unpacked"
   - Select the `dist` folder from this project

3. **Verify installation**:
   - You should see "Netflix Skipper" in your extensions list
   - The extension icon should appear in your Chrome toolbar

## Testing Steps

### 1. Basic Functionality Test

1. **Go to Netflix**: Navigate to https://www.netflix.com
2. **Start watching any video**: Click on any movie or TV show
3. **Wait for video to load**: Make sure the video is playing
4. **Test arrow keys**:
   - Press **Left Arrow (←)**: Should skip backward 5 seconds (default)
   - Press **Right Arrow (→)**: Should skip forward 5 seconds (default)
   - You should see a brief indicator showing the skip direction and duration

### 2. Settings Test

1. **Open extension popup**: Click the Netflix Skipper icon in Chrome toolbar
2. **Change skip interval**: 
   - Try changing from 5 to 10 seconds
   - Settings should save automatically
3. **Test new interval**: Go back to Netflix and test arrow keys with new interval
4. **Verify persistence**: Refresh the page and test - settings should persist

### 3. Edge Cases to Test

1. **Input field test**: 
   - Try typing in Netflix search box
   - Arrow keys should work normally (not trigger skipping)

2. **Multiple tabs**: 
   - Open Netflix in multiple tabs
   - Extension should only work on the active video

3. **Episode changes**: 
   - Let an episode end and auto-play next episode
   - Arrow keys should continue working (tests SPA navigation handling)

## Expected Behavior

### ✅ Should Work:
- Arrow keys skip video by configured interval
- Visual indicator appears briefly when skipping
- Settings persist across browser sessions
- Works after episode changes/navigation
- Doesn't interfere with typing in input fields

### ❌ Should NOT Happen:
- No "Whoops! Something went wrong" Netflix errors
- Arrow keys shouldn't work on non-Netflix sites
- No interference with normal Netflix controls (spacebar, etc.)

## Debugging

### Check Console Logs:
1. **Open Developer Tools**: F12 or right-click → Inspect
2. **Go to Console tab**
3. **Look for Netflix Skipper logs**:
   - `[Netflix Skipper] Controller initialized...`
   - `[Netflix Skipper] Available session IDs: ...`
   - `[Netflix Skipper] Found active player for session: ...`
   - `[Netflix Skipper] Seeking from X s to Y s`

### Common Issues:
- **"Netflix API not available yet"**: Wait a moment for Netflix to load
- **"No active watch session found"**: Make sure you're on a `/watch/` page with video playing
- **Arrow keys not working**: Check console for errors, try refreshing page

## Technical Notes

This extension uses Netflix's internal API:
- `netflix.appContext.state.playerApp.getAPI().videoPlayer`
- Finds session IDs starting with "watch-"
- Uses `player.seek()` method for precise control
- Handles SPA navigation with MutationObserver

The approach is based on successful community extensions and avoids the DRM-protected HTML5 video element manipulation that causes Netflix errors.
